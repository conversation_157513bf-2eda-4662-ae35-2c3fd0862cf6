import React, { useState } from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

import { useTheme } from "@mui/material";
import Header from "../components/Headers/Header";
import { useQuery, gql } from "@apollo/client";
import {
  getDashboardTotal,
  getDashboardSales,
  getDashboardOrders,
  getOrdersByDateRange,
} from "../apollo";

import { withTranslation } from "react-i18next";
import {
  TrendingUp,
  ArrowRight,
  Users,
  Store,
  Car,
  Utensils,
} from "lucide-react";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const GET_DASHBOARD_TOTAL = gql`
  ${getDashboardTotal}
`;
const GET_DASHBOARD_SALES = gql`
  ${getDashboardSales}
`;
const GET_DASHBOARD_ORDERS = gql`
  ${getDashboardOrders}
`;
const GET_ORDERS = gql`
  ${getOrdersByDateRange}
`;

// Modern StatsCard Component
const StatsCard = ({
  title,
  value,
  icon: Icon,
  color = "orange",
  loading = false,
}) => {
  const colorClasses = {
    orange: "bg-orange-50 text-orange-600 border-orange-200",
    green: "bg-green-50 text-green-600 border-green-200",
    blue: "bg-blue-50 text-blue-600 border-blue-200",
    purple: "bg-purple-50 text-purple-600 border-purple-200",
  };

  const progressColors = {
    orange: "bg-orange-500",
    green: "bg-green-500",
    blue: "bg-blue-500",
    purple: "bg-purple-500",
  };

  const progressBgColors = {
    orange: "bg-orange-200",
    green: "bg-green-200",
    blue: "bg-blue-200",
    purple: "bg-purple-200",
  };

  return (
    <div className="bg-white rounded-lg border-0 shadow-sm hover:shadow-md transition-shadow duration-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-600">{title}</h3>
        <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
          <Icon className="w-4 h-4" />
        </div>
      </div>
      <div className="text-2xl font-bold text-gray-900 mb-3">
        {loading ? "..." : value}
      </div>
      <div className="flex items-center">
        <div className={`w-12 h-2 rounded-full ${progressBgColors[color]}`}>
          <div
            className={`h-full w-8 rounded-full ${progressColors[color]}`}
          ></div>
        </div>
      </div>
    </div>
  );
};

const Dashboard = (props) => {
  const { t } = props;
  const theme = useTheme();
  const restaurantId = localStorage.getItem("restaurantId");

  const dataLine = {
    datasets: {
      label: t("SalesAmount"),
      // label: 'Sales Amount',
      backgroundColor: theme.palette.secondary.darkest,
      borderColor: theme.palette.secondary.darkest,
    },
  };
  const dataBar = {
    datasets: {
      label: t("OrderCount"),
      // label: 'Order count',
      backgroundColor: theme.palette.warning.dark,
      borderColor: theme.palette.warning.dark,
    },
  };

  const intializeStartDate = () => {
    var d = new Date();
    d.setDate(d.getDate() - 7);
    return d.toISOString().substring(0, 10);
  };
  const [stateData, setStateData] = useState({
    startingDate: intializeStartDate(),
    endingDate: new Date().toISOString().substring(0, 10),
  });

  const {
    data: dataTotal,
    error: errorTotal,
    loading: loadingTotal,
  } = useQuery(GET_DASHBOARD_TOTAL, {
    variables: {
      startingDate: stateData.startingDate.toString(),
      endingDate: stateData.endingDate.toString(),
      restaurant: restaurantId,
    },
  });
  const {
    data: dataSales,
    error: errorSales,
    loading: loadingSales,
  } = useQuery(GET_DASHBOARD_SALES, {
    variables: {
      startingDate: stateData.startingDate.toString(),
      endingDate: stateData.endingDate.toString(),
      restaurant: restaurantId,
    },
  });
  const { data: dataOrders, loading: loadingOrders } = useQuery(
    GET_DASHBOARD_ORDERS,
    {
      variables: {
        startingDate: stateData.startingDate.toString(),
        endingDate: stateData.endingDate.toString(),
        restaurant: restaurantId,
      },
    }
  );

  const { data, loading: loadingQuery } = useQuery(GET_ORDERS, {
    variables: {
      startingDate: stateData.startingDate.toString(),
      endingDate: stateData.endingDate.toString(),
      restaurant: restaurantId,
    },
  });
  console.log("getOrdersByDateRange", data);

  return (
    <>
      <Header />
      <div className="space-y-8 p-6 bg-gray-50 min-h-screen">
        {errorTotal ? (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {`${Error} + ${errorTotal.message}`}
          </div>
        ) : null}

        {/* Welcome Section */}
        <div className="relative overflow-hidden">
          <div className="bg-gradient-to-r from-orange-500 via-orange-600 to-red-500 border-0 shadow-xl rounded-lg">
            <div className="p-8">
              <div className="flex items-center justify-between relative z-10">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <h1 className="text-3xl font-bold text-white">
                      {t("Welcome back, Admin!")}
                    </h1>
                    <p className="text-orange-100 text-lg">
                      {t("Your food delivery empire at a glance")}
                    </p>
                  </div>
                  <button className="bg-white text-orange-600 hover:bg-orange-50 font-semibold px-6 py-2 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg flex items-center">
                    {t("View Analytics")}{" "}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </button>
                </div>
                <div className="hidden md:block">
                  <div className="w-32 h-32 bg-white/10 backdrop-blur-sm rounded-3xl flex items-center justify-center">
                    <div className="text-6xl">🚀</div>
                  </div>
                </div>
              </div>
              {/* Decorative elements */}
              <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
            </div>
          </div>
        </div>

        {/* Date Filter Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="mb-4">
            <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-orange-500" />
              {t("GraphFilter")}
            </h2>
          </div>
          <form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t("StartDate")}
                </label>
                <input
                  type="date"
                  max={new Date().toISOString().substring(0, 10)}
                  onChange={(event) => {
                    setStateData({
                      ...stateData,
                      startingDate: event.target.value,
                    });
                  }}
                  value={stateData.startingDate}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t("EndDate")}
                </label>
                <input
                  type="date"
                  max={new Date().toISOString().substring(0, 10)}
                  onChange={(event) => {
                    setStateData({
                      ...stateData,
                      endingDate: event.target.value,
                    });
                  }}
                  value={stateData.endingDate}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors"
                />
              </div>
            </div>
            <button
              type="button"
              className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold py-2 px-6 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg"
            >
              {t("Apply")}
            </button>
          </form>
        </div>
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title={t("TotalOrders")}
            value={
              loadingTotal
                ? "..."
                : dataTotal && dataTotal.getDashboardTotal.totalOrders
            }
            icon={Store}
            color="blue"
            loading={loadingTotal}
          />
          <StatsCard
            title="COD Orders"
            value={
              loadingQuery
                ? "..."
                : data && data.getOrdersByDateRange.countCashOnDeliveryOrders
            }
            icon={Car}
            color="green"
            loading={loadingQuery}
          />
          <StatsCard
            title={t("TotalSales")}
            value={
              loadingTotal
                ? "..."
                : dataTotal && dataTotal.getDashboardTotal.totalSales
            }
            icon={Utensils}
            color="orange"
            loading={loadingTotal}
          />
          <StatsCard
            title="COD Sales"
            value={
              loadingQuery
                ? "..."
                : data && data.getOrdersByDateRange.totalAmountCashOnDelivery
            }
            icon={Users}
            color="purple"
            loading={loadingQuery}
          />
        </div>

        {/* Chart and Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t("SalesAmount")} & {t("OrderCount")}
              </h3>
              <div className="relative">
                {errorSales ? (
                  <div className="text-red-500 text-center py-8">
                    Error loading chart data
                  </div>
                ) : (
                  <Line
                    height={400}
                    data={{
                      labels: loadingSales
                        ? []
                        : dataSales &&
                          dataSales.getDashboardSales.orders.map((d) => d.day),
                      datasets: [
                        {
                          ...dataLine.datasets,
                          data: loadingSales
                            ? []
                            : dataSales &&
                              dataSales.getDashboardSales.orders.map(
                                (d) => d.amount
                              ),
                          lineTension: 0.8,
                        },
                        {
                          ...dataBar.datasets,
                          data: loadingOrders
                            ? []
                            : dataOrders &&
                              dataOrders.getDashboardOrders.orders.map(
                                (d) => d.count
                              ),
                        },
                      ],
                    }}
                    options={{
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          labels: {
                            display: true,
                            color: theme.palette.text.primary,
                            font: {
                              size: 12,
                            },
                          },
                        },
                      },
                      scales: {
                        y: {
                          grid: {
                            color: theme.palette.divider,
                          },
                          ticks: {
                            color: theme.palette.text.secondary,
                            font: {
                              size: 12,
                            },
                          },
                        },
                        x: {
                          grid: {
                            color: theme.palette.divider,
                          },
                          ticks: {
                            color: theme.palette.text.secondary,
                            font: {
                              size: 12,
                            },
                          },
                        },
                      },
                    }}
                  />
                )}
              </div>
            </div>
          </div>
          <div className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-md rounded-lg">
            <div className="p-6 pb-4">
              <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-orange-500" />
                {t("Quick Actions")}
              </h3>
            </div>
            <div className="p-6 pt-0 space-y-3">
              <button className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold py-3 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg">
                {t("Add New Restaurant")}
              </button>
              <button className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-3 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg">
                {t("Add New Vendor")}
              </button>
              <button className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg">
                {t("Add New Rider")}
              </button>
              <button className="w-full border-2 border-gray-200 hover:border-orange-300 hover:bg-orange-50 font-semibold py-3 rounded-lg transition-all duration-200 bg-white text-gray-700">
                {t("View All Orders")}
              </button>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white border-0 shadow-md rounded-lg">
          <div className="p-6 pb-4">
            <h3 className="text-xl font-bold text-gray-900">
              {t("Recent Activity")}
            </h3>
          </div>
          <div className="p-6 pt-0">
            <div className="space-y-4">
              {[
                {
                  action: t("New order from McDonald's"),
                  time: t("2 minutes ago"),
                  status: "success",
                },
                {
                  action: t("Rider John completed delivery"),
                  time: t("5 minutes ago"),
                  status: "success",
                },
                {
                  action: t("New vendor registration"),
                  time: t("10 minutes ago"),
                  status: "info",
                },
                {
                  action: t("Payment processed"),
                  time: t("15 minutes ago"),
                  status: "success",
                },
              ].map((activity, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        activity.status === "success"
                          ? "bg-green-500"
                          : "bg-blue-500"
                      }`}
                    ></div>
                    <span className="text-gray-900 font-medium">
                      {activity.action}
                    </span>
                  </div>
                  <span className="text-gray-500 text-sm">{activity.time}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default withTranslation()(Dashboard);
