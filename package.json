{"name": "enatega-multivendor", "version": "2.0.0", "description": "Enatega MultiVendor dashboard", "main": "index.js", "homepage": "https://multivendor-admin.ninjascode.com/", "scripts": {"analyze": "source-map-explorer 'build/static/js/*.js'", "start": "NODE_OPTIONS=--openssl-legacy-provider react-scripts start", "build": "NODE_OPTIONS=--openssl-legacy-provider react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint:fix": "eslint . --ext .js,.jsx --fix", "format": "prettier --write '**/*.{js,jsx}'", "start:staging": "NODE_OPTIONS=--openssl-legacy-provider env-cmd -f .env.staging react-scripts start", "start:dev": "NODE_OPTIONS=--openssl-legacy-provider SET PORT=3000 env-cmd -f .env.development react-scripts start", "start:prod": "NODE_OPTIONS=--openssl-legacy-provider env-cmd -f .env.production react-scripts start", "build:dev": "NODE_OPTIONS=--openssl-legacy-provider env-cmd -f .env.dev react-scripts build", "build:prod": "NODE_OPTIONS=--openssl-legacy-provider env-cmd -f .env.production react-scripts build", "build:staging": "NODE_OPTIONS=--openssl-legacy-provider env-cmd -f .env.staging react-scripts build"}, "eslintConfig": {"extends": "react-app"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "dependencies": {"@apollo/client": "^3.5.8", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@craco/craco": "^7.1.0", "@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@material-ui/core": "^4.12.4", "@material-ui/lab": "^4.0.0-alpha.61", "@mui/icons-material": "^5.8.3", "@mui/material": "^5.8.3", "@mui/styles": "^5.8.3", "@react-google-maps/api": "^2.0.0", "@sentry/react": "^6.16.1", "@sentry/tracing": "^6.16.1", "@tailwindcss/postcss": "^4.1.11", "@wojtekmaj/react-timerange-picker": "^3.0.0", "apollo-boost": "^0.4.9", "babel-preset-react-app": "^10.0.1", "chart.js": "^3.6.0", "clsx": "^2.1.1", "env-cmd": "^10.1.0", "firebase": "^9.4.0", "flag": "^5.0.1", "graphql": "^15.0.0", "husky": "^8.0.3", "i18next": "^17.0.0", "lucide-react": "^0.525.0", "moment": "2.24.0", "nouislider": "13.1.1", "react": "^17.0.0", "react-chartjs-2": "^4.0.0", "react-data-table-component": "^6.9.3", "react-datetime": "2.16.3", "react-dom": "^17.0.0", "react-i18next": "^10.10.0", "react-loader-spinner": "^4.0.0", "react-notifications": "^1.7.4", "react-router-dom": "5.1.2", "react-to-print": "^2.15.1", "sass": "^1.79.4", "source-map-explorer": "^2.5.2", "styled-components": "^5.1.1", "subscriptions-transport-ws": "^0.11.0", "tailwind-merge": "^3.3.1", "validate.js": "^0.12.0"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/googlemaps": "3.30.18", "@types/markerclustererplus": "2.1.33", "@types/react": "^17.0.0", "autoprefixer": "^10.4.21", "husky": "^4.2.5", "lint-staged": "^10.2.7", "postcss": "^8.5.6", "prettier": "2.0.5", "prettier-config-standard": "^1.0.1", "react-scripts": "^5.0.1", "tailwindcss": "^4.1.11", "typescript": "3.3.3333"}}